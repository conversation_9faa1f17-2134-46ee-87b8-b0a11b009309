"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { toast } from "react-hot-toast"
import { 
  Calendar as CalendarIcon,
  Clock,
  User,
  Sparkles,
  MessageSquare,
  Video,
  ArrowRight
} from "lucide-react"
import { format } from "date-fns"

const consultationSchema = z.object({
  expertType: z.string().min(1, "Please select an expert type"),
  date: z.date({
    required_error: "Please select a date",
  }),
  time: z.string().min(1, "Please select a time"),
  consultationType: z.enum(["video", "phone", "in-person"]),
  description: z.string().min(10, "Please provide more details about your needs"),
  petType: z.string().min(1, "Please select a pet type"),
})

type ConsultationFormData = z.infer<typeof consultationSchema>

const EXPERT_TYPES = [
  "Veterinarian",
  "Behaviorist",
  "Training Specialist",
  "Nutrition Expert",
  "General Pet Care Advisor"
]

const PET_TYPES = [
  "Dog",
  "Cat",
  "Small Animal",
  "Bird",
  "Reptile",
  "Other"
]

const TIME_SLOTS = [
  "09:00 AM",
  "10:00 AM",
  "11:00 AM",
  "01:00 PM",
  "02:00 PM",
  "03:00 PM",
  "04:00 PM"
]

export default function ConsultationPage() {
  const [selectedDate, setSelectedDate] = useState<Date>()
  const [booking, setBooking] = useState(false)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ConsultationFormData>({
    resolver: zodResolver(consultationSchema),
  })

  const consultationType = watch("consultationType")

  const onSubmit = async (data: ConsultationFormData) => {
    setBooking(true)
    try {
      const response = await fetch("/api/consultation/schedule", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (response.ok) {
        toast.success("Consultation scheduled successfully!")
        // Redirect to confirmation page or dashboard
      } else {
        const error = await response.json()
        toast.error(error.message || "Failed to schedule consultation")
      }
    } catch (error) {
      console.error("Error scheduling consultation:", error)
      toast.error("Something went wrong")
    } finally {
      setBooking(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-50 to-white py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Expert Pet Consultation
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Get professional advice and support from our team of pet care experts
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Card>
            <CardContent className="p-6 text-center">
              <Video className="h-8 w-8 mx-auto mb-4 text-green-600" />
              <h3 className="font-semibold text-gray-900 mb-2">Video Chat</h3>
              <p className="text-gray-600">Connect face-to-face with our experts</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <MessageSquare className="h-8 w-8 mx-auto mb-4 text-green-600" />
              <h3 className="font-semibold text-gray-900 mb-2">Phone Call</h3>
              <p className="text-gray-600">Get advice over a phone consultation</p>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <User className="h-8 w-8 mx-auto mb-4 text-green-600" />
              <h3 className="font-semibold text-gray-900 mb-2">In-Person</h3>
              <p className="text-gray-600">Visit our facility for direct consultation</p>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Sparkles className="h-5 w-5 mr-2" />
              Schedule a Consultation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="expertType">Type of Expert</Label>
                  <Select onValueChange={(value) => setValue("expertType", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select expert type" />
                    </SelectTrigger>
                    <SelectContent>
                      {EXPERT_TYPES.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.expertType && (
                    <p className="text-sm text-red-500">{errors.expertType.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="petType">Pet Type</Label>
                  <Select onValueChange={(value) => setValue("petType", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select pet type" />
                    </SelectTrigger>
                    <SelectContent>
                      {PET_TYPES.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.petType && (
                    <p className="text-sm text-red-500">{errors.petType.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Consultation Type</Label>
                <div className="grid grid-cols-3 gap-4">
                  <Button
                    type="button"
                    variant={consultationType === "video" ? "default" : "outline"}
                    className="w-full"
                    onClick={() => setValue("consultationType", "video")}
                  >
                    <Video className="h-4 w-4 mr-2" />
                    Video
                  </Button>
                  <Button
                    type="button"
                    variant={consultationType === "phone" ? "default" : "outline"}
                    className="w-full"
                    onClick={() => setValue("consultationType", "phone")}
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Phone
                  </Button>
                  <Button
                    type="button"
                    variant={consultationType === "in-person" ? "default" : "outline"}
                    className="w-full"
                    onClick={() => setValue("consultationType", "in-person")}
                  >
                    <User className="h-4 w-4 mr-2" />
                    In-Person
                  </Button>
                </div>
                {errors.consultationType && (
                  <p className="text-sm text-red-500">{errors.consultationType.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label>Select Date</Label>
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={(date) => {
                      setSelectedDate(date)
                      if (date) setValue("date", date)
                    }}
                    className="rounded-md border"
                    disabled={(date) =>
                      date < new Date() || date > new Date(Date.now() + 12096e5)
                    }
                  />
                  {errors.date && (
                    <p className="text-sm text-red-500">{errors.date.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label>Select Time</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {TIME_SLOTS.map((time) => (
                      <Button
                        key={time}
                        type="button"
                        variant={watch("time") === time ? "default" : "outline"}
                        className="w-full"
                        onClick={() => setValue("time", time)}
                      >
                        <Clock className="h-4 w-4 mr-2" />
                        {time}
                      </Button>
                    ))}
                  </div>
                  {errors.time && (
                    <p className="text-sm text-red-500">{errors.time.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Consultation Details</Label>
                <Textarea
                  id="description"
                  {...register("description")}
                  rows={4}
                  placeholder="Please describe your pet's needs and any specific concerns..."
                />
                {errors.description && (
                  <p className="text-sm text-red-500">{errors.description.message}</p>
                )}
              </div>

              <div className="flex justify-end">
                <Button
                  type="submit"
                  disabled={booking}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {booking ? (
                    "Scheduling..."
                  ) : (
                    <>
                      Schedule Consultation
                      <ArrowRight className="h-4 w-4 ml-2" />
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        <div className="mt-12">
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Why Choose Our Experts?
              </h3>
              <ul className="space-y-4">
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                    <Check className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Certified Professionals</h4>
                    <p className="text-gray-600">All our experts are certified and have extensive experience in pet care</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                    <Check className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Flexible Scheduling</h4>
                    <p className="text-gray-600">Choose from various time slots that work best for you</p>
                  </div>
                </li>
                <li className="flex items-start">
                  <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-1">
                    <Check className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">Comprehensive Support</h4>
                    <p className="text-gray-600">Get expert advice on behavior, health, nutrition, and more</p>
                  </div>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
