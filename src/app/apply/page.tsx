"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  ArrowRight, 
  PawPrint, 
  CalendarRange, 
  Home,
  Users,
  Clock,
  Check
} from "lucide-react"
import { useRouter } from "next/navigation"
import { toast } from "react-hot-toast"

export default function ApplicationPage() {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Simulated API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      router.push("/apply/success")
      toast.success("Application submitted successfully!")
    } catch (error) {
      toast.error("Failed to submit application. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-50 to-white py-12">
      <div className="max-w-3xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Adoption Application
          </h1>
          <p className="text-xl text-gray-600">
            Fill out this form to begin your adoption journey
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              Step {step} of 4: {
                step === 1 ? "Personal Information" :
                step === 2 ? "Living Situation" :
                step === 3 ? "Pet Experience" :
                "Additional Information"
              }
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {step === 1 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      <Input id="firstName" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input id="lastName" required />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input id="phone" type="tel" required />
                  </div>
                </div>
              )}

              {step === 2 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="residence">Type of Residence</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select residence type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="house">House</SelectItem>
                        <SelectItem value="apartment">Apartment</SelectItem>
                        <SelectItem value="condo">Condo</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ownership">Own or Rent?</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select ownership status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="own">Own</SelectItem>
                        <SelectItem value="rent">Rent</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="household">Number of People in Household</Label>
                    <Input id="household" type="number" min="1" required />
                  </div>
                </div>
              )}

              {step === 3 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="experience">Previous Pet Experience</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select experience level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="first">First-time Owner</SelectItem>
                        <SelectItem value="some">Some Experience</SelectItem>
                        <SelectItem value="experienced">Experienced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currentPets">Current Pets</Label>
                    <Textarea 
                      id="currentPets" 
                      placeholder="Please list any current pets in your household"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="vetInfo">Veterinarian Information</Label>
                    <Textarea 
                      id="vetInfo" 
                      placeholder="Name and contact information of your current/previous veterinarian"
                    />
                  </div>
                </div>
              )}

              {step === 4 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="schedule">Typical Daily Schedule</Label>
                    <Textarea 
                      id="schedule" 
                      placeholder="Describe your typical daily schedule"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="preparation">Pet Preparation</Label>
                    <Textarea 
                      id="preparation" 
                      placeholder="How have you prepared for a new pet?"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="questions">Questions or Comments</Label>
                    <Textarea 
                      id="questions" 
                      placeholder="Any additional questions or comments?"
                    />
                  </div>
                </div>
              )}

              <div className="flex justify-between pt-4">
                {step > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setStep(step - 1)}
                  >
                    Previous
                  </Button>
                )}
                {step < 4 ? (
                  <Button
                    type="button"
                    onClick={() => setStep(step + 1)}
                    className="ml-auto"
                  >
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    disabled={loading}
                    className="ml-auto"
                  >
                    {loading ? (
                      "Submitting..."
                    ) : (
                      <>
                        Submit Application
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </>
                    )}
                  </Button>
                )}
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Application Steps */}
        <div className="mt-8 grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card className={step >= 1 ? "border-primary" : ""}>
            <CardContent className="p-4 text-center">
              <Users className="w-6 h-6 mx-auto mb-2" />
              <p className="text-sm font-medium">Personal Info</p>
            </CardContent>
          </Card>
          <Card className={step >= 2 ? "border-primary" : ""}>
            <CardContent className="p-4 text-center">
              <Home className="w-6 h-6 mx-auto mb-2" />
              <p className="text-sm font-medium">Living Situation</p>
            </CardContent>
          </Card>
          <Card className={step >= 3 ? "border-primary" : ""}>
            <CardContent className="p-4 text-center">
              <PawPrint className="w-6 h-6 mx-auto mb-2" />
              <p className="text-sm font-medium">Pet Experience</p>
            </CardContent>
          </Card>
          <Card className={step >= 4 ? "border-primary" : ""}>
            <CardContent className="p-4 text-center">
              <Clock className="w-6 h-6 mx-auto mb-2" />
              <p className="text-sm font-medium">Additional Info</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
