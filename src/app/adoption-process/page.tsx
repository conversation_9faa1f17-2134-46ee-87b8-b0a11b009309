"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Heart, 
  ClipboardCheck, 
  Home, 
  UserCheck, 
  Calendar,
  DollarSign,
  FilePen,
  Clock,
  ArrowRight,
  CheckCircle2
} from "lucide-react"
import Link from "next/link"

const ADOPTION_STEPS = [
  {
    title: "Browse Available Pets",
    description: "Start by exploring our available pets and find your perfect match.",
    icon: Heart,
    action: {
      text: "Find Pets",
      href: "/pets"
    }
  },
  {
    title: "Submit Application",
    description: "Fill out our comprehensive adoption application form.",
    icon: FilePen,
    action: {
      text: "Start Application",
      href: "/apply"
    }
  },
  {
    title: "Initial Review",
    description: "Our team reviews your application within 1-2 business days.",
    icon: ClipboardCheck,
    timeframe: "1-2 days"
  },
  {
    title: "Meet & Greet",
    description: "Schedule a visit to meet your potential new family member.",
    icon: Calendar,
    action: {
      text: "Schedule Visit",
      href: "/schedule-visit"
    }
  },
  {
    title: "Home Check",
    description: "A brief visit to ensure your home is ready for a new pet.",
    icon: Home,
    timeframe: "30-60 minutes"
  },
  {
    title: "Final Approval",
    description: "Application approval and adoption fee payment.",
    icon: UserCheck
  },
  {
    title: "Welcome Home",
    description: "Take your new family member to their forever home!",
    icon: CheckCircle2
  }
]

const AdoptionProcessPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-pink-50 to-white py-12">
      {/* Hero Section */}
      <div className="container mx-auto px-4 text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Our Adoption Process
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          We're here to help you find your perfect companion and ensure a smooth adoption journey.
          Here's what you can expect:
        </p>
      </div>

      {/* Timeline */}
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {ADOPTION_STEPS.map((step, index) => (
            <div key={step.title} className="relative">
              {/* Connector Line */}
              {index !== ADOPTION_STEPS.length - 1 && (
                <div className="absolute left-8 top-16 bottom-0 w-0.5 bg-primary/20" />
              )}
              
              {/* Step Card */}
              <Card className="mb-8 relative z-10">
                <CardHeader>
                  <div className="flex items-start space-x-4">
                    <div className="bg-primary/10 p-3 rounded-full">
                      <step.icon className="w-6 h-6 text-primary" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="flex items-center gap-3">
                        {step.title}
                        {step.timeframe && (
                          <span className="text-sm font-normal text-gray-500 flex items-center">
                            <Clock className="w-4 h-4 mr-1" />
                            {step.timeframe}
                          </span>
                        )}
                      </CardTitle>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{step.description}</p>
                  {step.action && (
                    <Link href={step.action.href}>
                      <Button variant="outline" className="group">
                        {step.action.text}
                        <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                  )}
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      {/* FAQ Section */}
      <div className="container mx-auto px-4 mt-16">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Have Questions?</h2>
          <p className="text-gray-600 mb-8">
            Check out our frequently asked questions or contact our adoption team.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/faq">
              <Button variant="outline" size="lg">
                View FAQ
              </Button>
            </Link>
            <Link href="/contact-shelter">
              <Button size="lg">
                Contact Us
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Adoption Fee Information */}
      <div className="container mx-auto px-4 mt-16">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="w-5 h-5 text-primary" />
                Adoption Fees
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Our adoption fees help cover the cost of care for our animals, including:
              </p>
              <ul className="grid md:grid-cols-2 gap-4">
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="w-5 h-5 text-green-500" />
                  <span>Spaying/Neutering</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="w-5 h-5 text-green-500" />
                  <span>Vaccinations</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="w-5 h-5 text-green-500" />
                  <span>Microchipping</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="w-5 h-5 text-green-500" />
                  <span>Initial Health Check</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="w-5 h-5 text-green-500" />
                  <span>Deworming Treatment</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle2 className="w-5 h-5 text-green-500" />
                  <span>Health Insurance (30 days)</span>
                </li>
              </ul>
              <div className="mt-6 text-center">
                <Link href="/pets">
                  <Button className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
                    Start Your Adoption Journey
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default AdoptionProcessPage
